import { ShoppingCartIcon, UserIcon } from "lucide-react";
import Link from "next/link";
import React from "react";

const menu = () => {
  return (
  <div className="flex justify-end">
         <nav className="flex gap-6 w-full">
        <Link href="/cart">
          <UserIcon />
          <span>Sign In</span>
        </Link>
        <Link href="/cart">
          <ShoppingCartIcon />
          <span>Cart</span>
        </Link>
      </nav>
    </div>
  );
};

export default menu;
