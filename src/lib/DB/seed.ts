import { cwd } from "process";
import { connectToDatabase } from ".";
import products from "../data";
import Product from "./models/product.model";
import {loadEnvConfig} from '@next/env'

loadEnvConfig(cwd())

const main = async() =>{
    try {
        const productsData = [...products]
        await connectToDatabase(process.env.DATABASE_URL)
        await Product.deleteMany()
        const createdProduct = await Product.insertMany(productsData)

        console.log({createdProduct,message:"Products are inserated to DB"});
         
        process.exit(0)

    } catch (error) {
        console.log(error);
        throw new Error ('Faild to insert products')
        
    }
}

main() 