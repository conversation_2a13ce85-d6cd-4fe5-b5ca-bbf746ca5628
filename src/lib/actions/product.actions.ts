"use server";

import { connectToDatabase } from "../DB";
import Product, { IProduct } from "../DB/models/product.model";

export async function getAllCategory() {  
  await connectToDatabase();
  const category = await Product.find({ isPublished: true }).distinct(
    "category"
  );
  return category;
}

export async function getProductForCard({
  tag,
  limit = 4,
}: {
  tag: string;
  limit?: number;
}) {
  await connectToDatabase();
  const product = await Product.find(
    { tags: { $in: [tag] }, isPublished: true },
    {
      name: 1,
      href: { $concat: ["/product/", "$slug"] },
      image: { $arrayElemAt: ["$images", 0] },
    }
  )
    .sort({ createdAt: "desc" })
    .limit(limit);

  return JSON.parse(JSON.stringify(product)) as {
    name: string;
    href: string;
    image: string;
  }[];
}

export async function getProductByTag({tag, limit=10}:{
    tag: string,
    limit?: number
}){
    await connectToDatabase()
    const product = await Product.find({
        tags: {$in: [tag]},
        isPublished: true
    }).sort({createdAt: 'desc'}).limit(limit)

    return JSON.parse(JSON.stringify(product)) as IProduct[]
}
